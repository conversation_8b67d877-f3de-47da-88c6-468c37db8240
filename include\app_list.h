#ifndef APP_LIST_H
#define APP_LIST_H

#include "config.h"
#include "bluetooth_protocol.h"
#include "debug_utils.h"
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "file_handler.h"
#include "gif_player.h"

// 新的列表配置结构体
struct ListConfig
{
    uint8_t sequence;    // 序列号（1-32）
    uint8_t reserved[3]; // 预留字段

    // === GIF配置区域 ===
    char gifFilename[32]; // GIF文件名（空字符串表示无GIF）

    // === 文本配置区域 ===
    uint8_t fontType;         // 字体类型：0x00=16x16, 0x01=32x32
    uint8_t screenArea;       // 显示区域：0x01=上半屏, 0x02=下半屏, 0x03=全屏
    uint8_t displayDirection; // 显示方向：0x00=横向显示, 0x01=纵向显示
    uint8_t textColor[3];     // RGB文本颜色
    uint8_t bgColor[3];       // RGB背景颜色（叠层模式下无效）
    uint8_t effect;           // 特效类型：0x00=静态, 0x01=左滚, 0x02=右滚等
    uint8_t effectSpeed;      // 特效速度：1-10
    uint8_t borderStyle;      // 边框样式：0x00=无边框，0x01=实线边框，0x02=点线边框，0x03=角落边框，0x04=彩虹边框
    uint8_t borderColorIndex; // 边框颜色索引：0x00=红色，0x01=绿色，0x02=蓝色，0x03=黄色，0x04=紫色，0x05=青色，0x06=白色
    uint8_t borderEffect;     // 边框效果：0x00=静止显示，0x01=顺时针流动，0x02=逆时针流动，0x03=闪烁效果
    uint8_t borderSpeed;      // 边框速度：1-10（1=最慢，10=最快）
    uint8_t gradientMode;     // 渐变模式：0x00=无, 0x01=垂直, 0x02=水平
    uint8_t charCount;        // 字符数量：0表示无文本，1-100表示字符数
    uint8_t fontData[1000];   // 点阵数据（最大支持50个32x32字符）
};

// 显示类型枚举
enum DisplayType
{
    EMPTY_MODE = 0, // 空配置，跳过
    GIF_MODE,       // 纯GIF模式
    TEXT_MODE,      // 纯文本模式
    OVERLAY_MODE    // 叠层模式（GIF背景+文字前景）
};

// 全局变量声明
extern ListConfig tempConfigList;             // 临时配置区
extern ListConfig configList[MAX_LIST_ITEMS]; // 正式列表配置数组
extern uint8_t currentEditIndex;              // 当前编辑的列表索引（0-based）
extern uint8_t totalListItems;                // 当前列表项总数
extern bool isEditMode;                       // 是否处于编辑模式
extern uint8_t currentPlayIndex;              // 当前播放索引
extern bool isListPlaying;                    // 是否正在播放列表
extern DisplayType currentListDisplayType;    // 当前列表项显示类型（缓存）

// 核心函数声明
void initListSystem();                                // 初始化列表系统
void clearTempConfig();                               // 清空临时配置
void saveTempToList(uint8_t index);                   // 保存临时配置到列表
void loadListToTemp(uint8_t index);                   // 加载列表配置到临时区
bool isValidConfig(const ListConfig &config);         // 验证配置有效性
DisplayType getDisplayType(const ListConfig &config); // 获取显示类型
void applyListConfig(const ListConfig &config);       // 应用列表配置

// 列表管理函数
void setEditIndex(uint8_t index);          // 设置编辑索引
void saveToList();                         // 保存当前临时配置到列表
void toggleEditMode(bool enable);          // 切换编辑模式
void startListPlayback();                  // 开始列表播放
void stopListPlayback();                   // 停止列表播放
void clearAllList();                       // 清空所有列表项
void deleteListItem(uint8_t index);        // 删除指定列表项
void setGifFilename(const char *filename); // 设置GIF文件名到临时配置

// 列表播放相关函数
void updateListPlayback();                                // 更新列表播放状态
void playNextListItem();                                  // 播放下一个列表项
void applyTextConfigFromList(const ListConfig &config);   // 应用文本配置
void applyColorConfigFromList(const ListConfig &config);  // 应用颜色配置
void applyEffectConfigFromList(const ListConfig &config); // 应用特效配置
void applyBorderConfigFromList(const ListConfig &config); // 应用边框配置

// 蓝牙命令处理函数
void handleSetEditIndexCommand(const BluetoothFrame &frame);   // 处理设置编辑索引命令 (0x40)
void handleSaveToListCommand(const BluetoothFrame &frame);     // 处理保存到列表命令 (0x41)
void handleEditModeCommand(const BluetoothFrame &frame);       // 处理编辑模式命令 (0x42)
void handleStartListPlayCommand(const BluetoothFrame &frame);  // 处理开始列表播放命令 (0x43)
void handleStopListPlayCommand(const BluetoothFrame &frame);   // 处理停止列表播放命令 (0x44)
void handleClearListCommand(const BluetoothFrame &frame);      // 处理清空列表命令 (0x45)
void handleDeleteListItemCommand(const BluetoothFrame &frame); // 处理删除列表项命令 (0x46)
void handleSetGifFilenameCommand(const BluetoothFrame &frame); // 处理设置GIF文件名命令 (0x47)

// 配置数据更新函数（存储到tempConfigList）
void updateTempTextConfig(const BluetoothFrame &frame);   // 更新临时文本配置
void updateTempColorConfig(const BluetoothFrame &frame);  // 更新临时颜色配置
void updateTempEffectConfig(const BluetoothFrame &frame); // 更新临时特效配置
void updateTempBorderConfig(const BluetoothFrame &frame); // 更新临时边框配置
void updateTempDirectionConfig(uint8_t direction);        // 更新临时显示方向配置

// 调试和状态函数
void printListStatus();              // 打印列表状态
void printTempConfig();              // 打印临时配置
void printListConfig(uint8_t index); // 打印指定索引的列表配置
void printAllFiles();                // 🆕 列出LittleFS中的所有文件

#endif // APP_LIST_H