#ifndef DISPLAYDRIVER_H
#define DISPLAYDRIVER_H

#include <Arduino.h>
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include "config.h"

// 字体参数在config.h中定义，根据currentFontSize动态选择
// 移除固定宏定义，改为在代码中根据字体大小标志位选择对应参数

// 使用config.h中的屏幕尺寸定义
#define PANEL_RES_X SCREEN_WIDTH
#define PANEL_RES_Y SCREEN_HEIGHT

// 外部显示对象声明
extern MatrixPanel_I2S_DMA *dma_display;

// 外部字体大小标志位声明
extern uint8_t currentFontSize;

// 颜色定义
#define COLOR_WHITE 0xFFFF
#define COLOR_RED 0xF800
#define COLOR_GREEN 0x07E0
#define COLOR_BLUE 0x001F
#define COLOR_YELLOW 0xFFE0

// 函数声明
void drawChar16x16(int x, int y, const uint16_t *font_data, uint16_t color);
void drawChar16x16Gradient(int x, int y, const uint16_t *font_data, bool isUpper, uint8_t gradientMode);
void drawChar16x16Vertical(int x, int y, const uint16_t *font_data, uint16_t color);                                          // 竖向显示字符
void drawChar16x16VerticalGradient(int x, int y, const uint16_t *font_data, bool isUpper, uint8_t gradientMode);              // 竖向渐变字符
void drawChar16x16WithWave(int x, int y, const uint16_t *font_data, uint16_t baseColor, int charIndex, bool isUpper);         // 波浪特效字符
void drawChar16x16VerticalWithWave(int x, int y, const uint16_t *font_data, uint16_t baseColor, int charIndex, bool isUpper); // 竖向波浪特效字符
void drawString16x16(int x, int y, const uint16_t *font_array, int char_count, uint16_t color);
void drawString16x16Gradient(int x, int y, const uint16_t *font_array, int char_count, bool isUpper, uint8_t gradientMode);
void drawString16x16Vertical(int x, int y, const uint16_t *font_array, int char_count, uint16_t color);                                           // 竖向显示字符串
void drawString16x16VerticalGradient(int x, int y, const uint16_t *font_array, int char_count, bool isUpper, uint8_t gradientMode);               // 竖向渐变字符串
void drawString16x16WithWave(int x, int y, const uint16_t *font_array, int char_count, uint16_t baseColor, bool isUpper, int startIndex);         // 波浪特效字符串
void drawString16x16VerticalWithWave(int x, int y, const uint16_t *font_array, int char_count, uint16_t baseColor, bool isUpper, int startIndex); // 竖向波浪特效字符串
void drawString16x16WithShake(int x, int y, const uint16_t *font_array, int char_count, uint16_t color, bool isUpper, int startIndex);            // 抖动特效字符串
void drawString16x16VerticalWithShake(int x, int y, const uint16_t *font_array, int char_count, uint16_t color, bool isUpper, int startIndex);    // 竖向抖动特效字符串
void drawChar32x32(int x, int y, const uint16_t *font_data, uint16_t color);                                                                      // 32x32字符显示
void drawChar32x32Vertical(int x, int y, const uint16_t *font_data, uint16_t color);                                                              // 32x32竖向显示字符
void drawChar32x32Gradient(int x, int y, const uint16_t *font_data, uint8_t gradientMode);                                                        // 32x32字符渐变色显示
void drawChar32x32VerticalGradient(int x, int y, const uint16_t *font_data, uint8_t gradientMode);                                                // 32x32竖向字符渐变色显示
void drawChar32x32WithWave(int x, int y, const uint16_t *font_data, uint16_t baseColor, int charIndex);                                           // 32x32波浪特效字符
void drawChar32x32VerticalWithWave(int x, int y, const uint16_t *font_data, uint16_t baseColor, int charIndex);                                   // 32x32竖向波浪特效字符
void drawString32x32(int x, int y, const uint16_t *font_array, int char_count, uint16_t color);                                                   // 32x32字符串显示
void drawString32x32Vertical(int x, int y, const uint16_t *font_array, int char_count, uint16_t color);                                           // 32x32竖向显示字符串
void drawString32x32Gradient(int x, int y, const uint16_t *font_array, int char_count, uint8_t gradientMode);                                     // 32x32字符串渐变色显示
void drawString32x32VerticalGradient(int x, int y, const uint16_t *font_array, int char_count, uint8_t gradientMode);                             // 32x32竖向字符串渐变色显示
void drawString32x32WithWave(int x, int y, const uint16_t *font_array, int char_count, uint16_t baseColor, int startIndex);                       // 32x32波浪特效字符串
void drawString32x32VerticalWithWave(int x, int y, const uint16_t *font_array, int char_count, uint16_t baseColor, int startIndex);               // 32x32竖向波浪特效字符串
void drawString32x32WithShake(int x, int y, const uint16_t *font_array, int char_count, uint16_t color, int startIndex);                          // 32x32抖动特效字符串
void drawString32x32VerticalWithShake(int x, int y, const uint16_t *font_array, int char_count, uint16_t color, int startIndex);                  // 32x32竖向抖动特效字符串
uint16_t rgb888to565(uint8_t r, uint8_t g, uint8_t b);                                                                                            // RGB888转RGB565
uint16_t applyBrightnessToColor(uint16_t color, float brightness);                                                                                // 应用亮度系数到颜色（用于波浪特效）
uint16_t getGradientColor(int x, int y, bool isUpper, uint8_t gradientMode);                                                                      // 获取16x16渐变色
uint16_t getGradientColor32x32(int x, int y, uint8_t gradientMode);                                                                               // 获取32x32渐变色

// ==================== 新增字符级颜色控制函数 ====================
void drawString16x16WithCharColors(int x, int y, const uint16_t *font_array, int char_count, bool isUpper, int startIndex = 0);         // 16x16字符串字符级颜色显示
void drawString16x16VerticalWithCharColors(int x, int y, const uint16_t *font_array, int char_count, bool isUpper, int startIndex = 0); // 16x16竖向字符串字符级颜色显示
void drawString32x32WithCharColors(int x, int y, const uint16_t *font_array, int char_count, int startIndex = 0);                       // 32x32字符串字符级颜色显示
void drawString32x32VerticalWithCharColors(int x, int y, const uint16_t *font_array, int char_count, int startIndex = 0);               // 32x32竖向字符串字符级颜色显示

#endif