#include "app_list.h"
#include "DisplayDriver.h" // 包含currentFontSize等变量声明
#include "LEDController.h" // 包含更多变量和函数声明

// 全局变量定义
ListConfig tempConfigList;                       // 临时配置区
ListConfig configList[MAX_LIST_ITEMS];           // 正式列表配置数组
uint8_t currentEditIndex = 0;                    // 当前编辑的列表索引（0-based）
uint8_t totalListItems = 0;                      // 当前列表项总数
bool isEditMode = false;                         // 是否处于编辑模式
uint8_t currentPlayIndex = 0;                    // 当前播放索引
bool isListPlaying = false;                      // 是否正在播放列表
unsigned long lastListChangeTime = 0;            // 上次列表切换时间
const unsigned long LIST_ITEM_DURATION = 5000;   // 每个列表项播放时长（毫秒）
DisplayType currentListDisplayType = EMPTY_MODE; // 当前列表项的显示类型（缓存）

// 外部变量声明
extern loopstate currentloopstate;
extern uint8_t currentFontSize;

// 外部函数声明
extern void handleUpperTextCommand(const uint16_t *fontData, int charCount);
extern void handleLowerTextCommand(const uint16_t *fontData, int charCount);
extern void handleFullScreenTextCommand(const uint16_t *fontData, int charCount);
extern void handleColorCommand(const BluetoothFrame &frame);
extern void handleEffectCommand(const BluetoothFrame &frame);
extern void handleBorderCommand(const BluetoothFrame &frame);
extern void clearAllEffects(bool isUpper);
extern void clearBorderEffect();

// ==================== 核心函数实现 ====================

/**
 * 初始化列表系统
 */
void initListSystem()
{
    DEBUG_INFO("Initializing new list system...");

    // 清空所有配置
    memset(&tempConfigList, 0, sizeof(ListConfig));
    memset(configList, 0, sizeof(ListConfig) * MAX_LIST_ITEMS);

    // 重置状态变量
    currentEditIndex = 0;
    totalListItems = 0;
    isEditMode = false;
    currentPlayIndex = 0;
    isListPlaying = false;
    lastListChangeTime = 0;
    currentListDisplayType = EMPTY_MODE;

    DEBUG_INFO("✅ New list system initialized successfully");
}

/**
 * 清空临时配置（完全重置）
 */
void clearTempConfig()
{
    DEBUG_INFO("Clearing temporary configuration...");
    memset(&tempConfigList, 0, sizeof(ListConfig));
    DEBUG_INFO("✅ Temporary configuration cleared");
}

/**
 * 保存临时配置到列表
 */
void saveTempToList(uint8_t index)
{
    if (index >= MAX_LIST_ITEMS)
    {
        DEBUG_ERROR("❌ Invalid list index: %d (max: %d)", index, MAX_LIST_ITEMS - 1);
        return;
    }

    DEBUG_INFO("Saving temp config to list[%d]...", index);

    // 复制临时配置到正式列表
    memcpy(&configList[index], &tempConfigList, sizeof(ListConfig));
    configList[index].sequence = index + 1; // 设置序列号（1-based）

    // 更新列表项总数
    if (index >= totalListItems)
    {
        totalListItems = index + 1;
    }

    // 注意：这里不清空tempConfigList，保持数据
    DEBUG_INFO("✅ Temp config saved to list[%d], total items: %d", index, totalListItems);
}

/**
 * 加载列表配置到临时区（编辑模式）
 */
void loadListToTemp(uint8_t index)
{
    if (index >= MAX_LIST_ITEMS || index >= totalListItems)
    {
        DEBUG_ERROR("❌ Invalid list index: %d (total: %d)", index, totalListItems);
        return;
    }

    DEBUG_INFO("Loading list[%d] config to temp...", index);
    memcpy(&tempConfigList, &configList[index], sizeof(ListConfig));
    DEBUG_INFO("✅ List[%d] config loaded to temp", index);
}

/**
 * 验证配置有效性
 */
bool isValidConfig(const ListConfig &config)
{
    // GIF文件名验证
    if (strlen(config.gifFilename) > 0)
    {
        if (strlen(config.gifFilename) >= LIST_GIF_FILENAME_LEN)
        {
            DEBUG_ERROR("❌ GIF filename too long: %s", config.gifFilename);
            return false;
        }

        // 检查文件是否存在
        String fullPath = GIF_STORAGE_PATH + String(config.gifFilename);
        if (!LittleFS.exists(fullPath))
        {
            DEBUG_WARN("⚠️ GIF file not found: %s", config.gifFilename);
            // 不返回false，允许配置保存，播放时再处理
        }
    }

    // 文本配置验证
    if (config.charCount > 0)
    {
        if (config.charCount > MAX_LIST_CHARS)
        {
            DEBUG_ERROR("❌ Character count too large: %d (max: %d)", config.charCount, MAX_LIST_CHARS);
            return false;
        }

        if (config.fontType > 1)
        {
            DEBUG_ERROR("❌ Invalid font type: %d", config.fontType);
            return false;
        }

        if (config.screenArea < 1 || config.screenArea > 3)
        {
            DEBUG_ERROR("❌ Invalid screen area: %d", config.screenArea);
            return false;
        }
    }

    return true;
}

/**
 * 获取显示类型
 */
DisplayType getDisplayType(const ListConfig &config)
{
    bool hasGif = (strlen(config.gifFilename) > 0);
    bool hasText = (config.charCount > 0);

    if (hasGif && hasText)
    {
        DEBUG_INFO("Display type: OVERLAY_MODE (GIF + Text)");
        return OVERLAY_MODE; // 叠层模式（GIF背景+文字前景）
    }
    else if (hasGif)
    {
        DEBUG_INFO("Display type: GIF_MODE");
        return GIF_MODE; // 纯GIF模式
    }
    else if (hasText)
    {
        DEBUG_INFO("Display type: TEXT_MODE");
        return TEXT_MODE; // 纯文本模式
    }
    else
    {
        DEBUG_INFO("Display type: EMPTY_MODE (skipping)");
        return EMPTY_MODE; // 空配置，跳过
    }
}

// ==================== 蓝牙命令处理函数 ====================

/**
 * 处理设置编辑索引命令 (0x40)
 */
void handleSetEditIndexCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_SET_EDIT_INDEX_DATA_LEN)
    {
        DEBUG_ERROR("❌ Invalid set edit index command data");
        return;
    }

    uint8_t index = frame.data[0];
    if (index < 1 || index > MAX_LIST_ITEMS)
    {
        DEBUG_ERROR("❌ Invalid edit index: %d (range: 1-%d)", index, MAX_LIST_ITEMS);
        return;
    }

    DEBUG_INFO("📍 Received set edit index command: %d", index);
    currentEditIndex = index - 1; // 转换为0-based索引

    // 如果在编辑模式且索引有效，加载配置到临时区
    if (isEditMode && currentEditIndex < totalListItems)
    {
        loadListToTemp(currentEditIndex);
    }
}

/**
 * 处理保存文本配置到列表命令 (0x41) - 专门用于保存文本配置
 */
void handleSaveToListCommand(const BluetoothFrame &frame)
{
    DEBUG_INFO("📝 Received save text config to list command");

    // 检查临时配置中是否有文本数据
    if (tempConfigList.charCount == 0)
    {
        DEBUG_WARN("⚠️ No text data in temp config, nothing to save");
        return;
    }

    // 验证文本配置的有效性
    if (tempConfigList.charCount > MAX_LIST_CHARS)
    {
        DEBUG_ERROR("❌ Character count too large: %d (max: %d)", tempConfigList.charCount, MAX_LIST_CHARS);
        return;
    }

    if (tempConfigList.fontType > 1)
    {
        DEBUG_ERROR("❌ Invalid font type: %d", tempConfigList.fontType);
        return;
    }

    if (tempConfigList.screenArea < 1 || tempConfigList.screenArea > 3)
    {
        DEBUG_ERROR("❌ Invalid screen area: %d", tempConfigList.screenArea);
        return;
    }

    // 检查是否超出列表限制
    if (currentEditIndex >= MAX_LIST_ITEMS)
    {
        DEBUG_ERROR("❌ List is full, cannot add more items (max: %d)", MAX_LIST_ITEMS);
        return;
    }

    DEBUG_INFO("📝 Saving text config: font=%d, area=%d, chars=%d",
               tempConfigList.fontType, tempConfigList.screenArea, tempConfigList.charCount);

    // 🆕 简化逻辑：将文本配置保存到当前索引，如果当前索引超出范围则扩展列表
    if (currentEditIndex >= totalListItems)
    {
        // 扩展列表到当前索引
        for (uint8_t i = totalListItems; i <= currentEditIndex; i++)
        {
            memset(&configList[i], 0, sizeof(ListConfig));
            configList[i].sequence = i + 1;
        }
        totalListItems = currentEditIndex + 1;
    }

    // 保存文本配置到当前索引（保留GIF文件名如果有的话）
    DEBUG_INFO("📝 Saving text config to list[%d]", currentEditIndex);

    // 保存GIF文件名（如果存在）
    char existingGif[LIST_GIF_FILENAME_LEN];
    strncpy(existingGif, configList[currentEditIndex].gifFilename, LIST_GIF_FILENAME_LEN - 1);
    existingGif[LIST_GIF_FILENAME_LEN - 1] = '\0';

    // 复制临时配置到列表
    memcpy(&configList[currentEditIndex], &tempConfigList, sizeof(ListConfig));
    configList[currentEditIndex].sequence = currentEditIndex + 1;

    // 恢复GIF文件名（如果原来有的话）
    if (strlen(existingGif) > 0)
    {
        strncpy(configList[currentEditIndex].gifFilename, existingGif, LIST_GIF_FILENAME_LEN - 1);
        configList[currentEditIndex].gifFilename[LIST_GIF_FILENAME_LEN - 1] = '\0';
    }

    // 判断显示类型
    DisplayType type = getDisplayType(configList[currentEditIndex]);
    const char *typeNames[] = {"EMPTY", "GIF", "TEXT", "OVERLAY"};
    DEBUG_INFO("✅ Text set to list[%d], display type: %s", currentEditIndex, typeNames[type]);

    // 🆕 注意：0x41命令不递增索引，只有0x47才递增

    DEBUG_INFO("✅ Text configuration saved successfully");
}

/**
 * 处理编辑模式命令 (0x42)
 */
void handleEditModeCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_EDIT_MODE_DATA_LEN)
    {
        DEBUG_ERROR("❌ Invalid edit mode command data");
        return;
    }

    bool enable = (frame.data[0] != 0);
    DEBUG_INFO("🔧 Received edit mode command: %s", enable ? "ENABLE" : "DISABLE");
    isEditMode = enable;
}

/**
 * 处理开始列表播放命令 (0x43)
 */
void handleStartListPlayCommand(const BluetoothFrame &frame)
{
    DEBUG_INFO("▶️ Received start list playback command");

    // 🆕 打印当前列表状态和文件系统状态用于调试
    printListStatus();
    printAllFiles(); // 🆕 检查文件系统中的实际文件

    if (totalListItems == 0)
    {
        DEBUG_WARN("⚠️ No list items to play");
        DEBUG_INFO("🔍 Current list status:");
        printListStatus();
        return;
    }

    isListPlaying = true;
    currentPlayIndex = 0; // 从第一个项目开始
    currentloopstate = loopstate::loop_state_list_new;
    lastListChangeTime = millis();

    DEBUG_INFO("🎬 Starting list playback with %d items", totalListItems);

    // 立即播放第一个有效项目
    if (totalListItems > 0)
    {
        // 寻找第一个有效项目
        for (uint8_t i = 0; i < totalListItems; i++)
        {
            DisplayType type = getDisplayType(configList[i]);
            if (type != EMPTY_MODE)
            {
                currentPlayIndex = i;
                DEBUG_INFO("🎬 Playing first list item[%d], type: %d", currentPlayIndex, type);
                applyListConfig(configList[currentPlayIndex]);
                lastListChangeTime = millis(); // 设置初始时间戳
                break;
            }
        }
    }
}

/**
 * 处理停止列表播放命令 (0x44)
 */
void handleStopListPlayCommand(const BluetoothFrame &frame)
{
    DEBUG_INFO("⏹️ Received stop list playback command");
    isListPlaying = false;

    // 停止当前播放
    if (gifPlaying)
    {
        stopGIF();
    }

    // 清屏
    dma_display->clearScreen();

    // 切换到默认状态
    currentloopstate = loopstate::loop_state_text;
}

/**
 * 处理清空列表命令 (0x45)
 */
void handleClearListCommand(const BluetoothFrame &frame)
{
    DEBUG_INFO("🗑️ Received clear list command");

    // 停止播放
    if (isListPlaying)
    {
        handleStopListPlayCommand(frame);
    }

    // 清空所有配置
    memset(configList, 0, sizeof(ListConfig) * MAX_LIST_ITEMS);
    totalListItems = 0;
    currentEditIndex = 0;
    currentPlayIndex = 0;

    DEBUG_INFO("✅ All list items cleared");
}

/**
 * 处理删除列表项命令 (0x46)
 */
void handleDeleteListItemCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < BT_DELETE_LIST_ITEM_DATA_LEN)
    {
        DEBUG_ERROR("❌ Invalid delete list item command data");
        return;
    }

    uint8_t index = frame.data[0];
    if (index < 1 || index > totalListItems)
    {
        DEBUG_ERROR("❌ Invalid delete index: %d (range: 1-%d)", index, totalListItems);
        return;
    }

    uint8_t deleteIndex = index - 1; // 转换为0-based索引
    DEBUG_INFO("🗑️ Deleting list item[%d]", deleteIndex);

    // 将后面的项目前移
    for (uint8_t i = deleteIndex; i < totalListItems - 1; i++)
    {
        memcpy(&configList[i], &configList[i + 1], sizeof(ListConfig));
        configList[i].sequence = i + 1; // 更新序列号
    }

    // 清空最后一项
    memset(&configList[totalListItems - 1], 0, sizeof(ListConfig));
    totalListItems--;

    // 调整相关索引
    if (currentEditIndex >= totalListItems && totalListItems > 0)
    {
        currentEditIndex = totalListItems - 1;
    }
    if (currentPlayIndex >= totalListItems && totalListItems > 0)
    {
        currentPlayIndex = totalListItems - 1;
    }

    DEBUG_INFO("✅ List item deleted, new total: %d", totalListItems);
}

/**
 * 处理设置GIF文件名命令 (0x47) - 直接保存GIF配置到列表
 */
void handleSetGifFilenameCommand(const BluetoothFrame &frame)
{
    if (!frame.hasValidData() || frame.dataLength < 1 || frame.dataLength > BT_SET_GIF_FILENAME_MAX_LEN)
    {
        DEBUG_ERROR("❌ Invalid GIF filename command data length: %d", frame.dataLength);
        return;
    }

    // 构造文件名字符串
    char filename[BT_SET_GIF_FILENAME_MAX_LEN + 1];
    memcpy(filename, frame.data, frame.dataLength);
    filename[frame.dataLength] = '\0'; // 确保字符串结束

    DEBUG_INFO("📁 Setting and saving GIF filename: %s", filename);

    // 验证GIF文件是否存在
    String fullPath = GIF_STORAGE_PATH + String(filename);
    if (!LittleFS.exists(fullPath))
    {
        DEBUG_WARN("⚠️ GIF file not found: %s, but saving config anyway", filename);
    }

    // 检查是否超出列表限制
    if (currentEditIndex >= MAX_LIST_ITEMS)
    {
        DEBUG_ERROR("❌ List is full, cannot add more items (max: %d)", MAX_LIST_ITEMS);
        return;
    }

    // 🆕 简化逻辑：直接在当前索引设置GIF，如果有文本就是叠层，没有就是纯GIF
    DEBUG_INFO("📁 Setting GIF to list[%d]", currentEditIndex);

    // 如果当前索引没有配置，先创建空配置
    if (currentEditIndex >= totalListItems)
    {
        // 扩展列表到当前索引
        for (uint8_t i = totalListItems; i <= currentEditIndex; i++)
        {
            memset(&configList[i], 0, sizeof(ListConfig));
            configList[i].sequence = i + 1;
        }
        totalListItems = currentEditIndex + 1;
    }

    // 设置GIF文件名到当前配置项
    strncpy(configList[currentEditIndex].gifFilename, filename, LIST_GIF_FILENAME_LEN - 1);
    configList[currentEditIndex].gifFilename[LIST_GIF_FILENAME_LEN - 1] = '\0';

    // 判断显示类型
    DisplayType type = getDisplayType(configList[currentEditIndex]);
    const char *typeNames[] = {"EMPTY", "GIF", "TEXT", "OVERLAY"};
    DEBUG_INFO("✅ GIF set to list[%d], display type: %s", currentEditIndex, typeNames[type]);

    DEBUG_INFO("✅ GIF config saved to list[%d], total items: %d", currentEditIndex, totalListItems);

    // 🆕 关键：无论是否在编辑模式，都自动递增索引
    currentEditIndex++;
    if (currentEditIndex >= MAX_LIST_ITEMS)
    {
        currentEditIndex = MAX_LIST_ITEMS - 1; // 防止溢出
        DEBUG_WARN("⚠️ Reached maximum list items limit");
    }
    DEBUG_INFO("📈 Auto-incremented edit index to: %d", currentEditIndex);
}

/**
 * 更新临时颜色配置
 */
void updateTempColorConfig(const BluetoothFrame &frame)
{
    DEBUG_INFO("🎨 Updating temp color config...");

    if (frame.dataLength < BT_COLOR_DATA_LEN)
    {
        DEBUG_ERROR("❌ Invalid color frame data length: %d, expected: %d", frame.dataLength, BT_COLOR_DATA_LEN);
        return;
    }

    // 使用正确的颜色数据格式解析
    uint8_t screenArea, target, mode, r, g, b, gradientMode;
    frame.getColorData(screenArea, target, mode, r, g, b, gradientMode);

    // 更新tempConfigList中的颜色配置
    // 注意：不直接覆盖screenArea，而是根据已有配置智能处理

    if (target == BT_COLOR_TARGET_TEXT) // 文本颜色 (0x01)
    {
        tempConfigList.textColor[0] = r;
        tempConfigList.textColor[1] = g;
        tempConfigList.textColor[2] = b;

        DEBUG_INFO("📝 Text color updated for screenArea: %d", screenArea);
    }
    else if (target == BT_COLOR_TARGET_BACKGROUND) // 背景颜色 (0x02)
    {
        tempConfigList.bgColor[0] = r;
        tempConfigList.bgColor[1] = g;
        tempConfigList.bgColor[2] = b;

        DEBUG_INFO("🎨 Background color updated for screenArea: %d", screenArea);
    }
    else
    {
        DEBUG_WARN("⚠️ Unknown color target: %d, ignoring", target);
    }

    // 渐变模式可以更新，不会冲突
    tempConfigList.gradientMode = gradientMode;

    DEBUG_INFO("✅ Color config updated: area=%d, target=%d, RGB(%d,%d,%d), gradient=%d",
               screenArea, target, r, g, b, gradientMode);
}

/**
 * 更新临时特效配置
 */
void updateTempEffectConfig(const BluetoothFrame &frame)
{
    DEBUG_INFO("✨ Updating temp effect config...");

    if (frame.dataLength < 3)
    {
        DEBUG_ERROR("❌ Invalid effect frame data length: %d", frame.dataLength);
        return;
    }

    uint8_t screenArea = frame.data[0];
    uint8_t effectType = frame.data[1];
    uint8_t effectSpeed = frame.data[2];

    // 更新tempConfigList中的特效配置
    tempConfigList.effect = effectType;
    tempConfigList.effectSpeed = effectSpeed;

    DEBUG_INFO("✨ Effect config updated for screenArea: %d", screenArea);

    DEBUG_INFO("✅ Effect config updated: area=%d, type=%d, speed=%d",
               screenArea, effectType, effectSpeed);
}

/**
 * 更新临时显示方向配置
 */
void updateTempDirectionConfig(uint8_t direction)
{
    DEBUG_INFO("🔄 Updating temp direction config...");

    // 更新tempConfigList中的显示方向配置
    tempConfigList.displayDirection = direction;

    DEBUG_INFO("✅ Direction config updated: direction=%s",
               (direction == BT_DIRECTION_HORIZONTAL) ? "HORIZONTAL" : "VERTICAL");
}

/**
 * 更新临时边框配置
 */
void updateTempBorderConfig(const BluetoothFrame &frame)
{
    DEBUG_INFO("🔲 Updating temp border config...");

    if (frame.dataLength < 4)
    {
        DEBUG_ERROR("❌ Invalid border frame data length: %d", frame.dataLength);
        return;
    }

    uint8_t borderStyle = frame.data[0];
    uint8_t borderColorIndex = frame.data[1];
    uint8_t borderEffect = frame.data[2];
    uint8_t borderSpeed = frame.data[3];

    // 更新tempConfigList中的边框配置
    tempConfigList.borderStyle = borderStyle;
    tempConfigList.borderColorIndex = borderColorIndex;
    tempConfigList.borderEffect = borderEffect;
    tempConfigList.borderSpeed = borderSpeed;

    DEBUG_INFO("✅ Border config updated: style=%d, color=%d, effect=%d, speed=%d",
               borderStyle, borderColorIndex, borderEffect, borderSpeed);
}

// ==================== 配置数据更新函数 ====================

/**
 * 更新临时文本配置
 */
void updateTempTextConfig(const BluetoothFrame &frame)
{
    DEBUG_INFO("📝 Updating temp text config...");

    if (currentFontSize == BT_FONT_32x32)
    {
        // 32x32字体处理
        uint8_t screenArea;
        int charCount;
        const uint16_t *fontData = frame.getFontData32x32(screenArea, charCount);

        if (fontData && charCount > 0)
        {
            tempConfigList.fontType = 1; // 32x32
            tempConfigList.screenArea = screenArea;
            tempConfigList.charCount = charCount;

            // 复制字体数据
            size_t dataSize = charCount * 64 * sizeof(uint16_t); // 32x32字体每字符64个uint16_t
            if (dataSize <= LIST_FONT_DATA_SIZE)
            {
                memcpy(tempConfigList.fontData, fontData, dataSize);
                DEBUG_INFO("✅ 32x32 text config updated: %d chars", charCount);
            }
            else
            {
                DEBUG_ERROR("❌ Font data too large: %d bytes (max: %d)", dataSize, LIST_FONT_DATA_SIZE);
            }
        }
    }
    else
    {
        // 16x16字体处理
        uint8_t screenArea;
        int charCount;
        const uint16_t *fontData = frame.getFontData16x16(screenArea, charCount);

        if (fontData && charCount > 0)
        {
            tempConfigList.fontType = 0; // 16x16
            tempConfigList.screenArea = screenArea;
            tempConfigList.charCount = charCount;

            // 复制字体数据
            size_t dataSize = charCount * 16 * sizeof(uint16_t); // 16x16字体每字符16个uint16_t
            if (dataSize <= LIST_FONT_DATA_SIZE)
            {
                memcpy(tempConfigList.fontData, fontData, dataSize);
                DEBUG_INFO("✅ 16x16 text config updated: %d chars", charCount);
            }
            else
            {
                DEBUG_ERROR("❌ Font data too large: %d bytes (max: %d)", dataSize, LIST_FONT_DATA_SIZE);
            }
        }
    }
}

/**
 * 更新列表播放状态
 */
void updateListPlayback()
{
    if (!isListPlaying || totalListItems == 0)
    {
        return;
    }

    // 检查是否需要切换到下一项
    unsigned long currentTime = millis();
    if (currentTime - lastListChangeTime >= LIST_ITEM_DURATION)
    {
        playNextListItem();
    }
}

/**
 * 播放下一个列表项
 */
void playNextListItem()
{
    if (!isListPlaying || totalListItems == 0)
    {
        return;
    }

    // 寻找下一个有效的配置项
    uint8_t startIndex = currentPlayIndex;
    uint8_t attempts = 0;

    do
    {
        currentPlayIndex = (currentPlayIndex + 1) % totalListItems;
        attempts++;

        DisplayType type = getDisplayType(configList[currentPlayIndex]);
        if (type != EMPTY_MODE)
        {
            DEBUG_INFO("🎬 Playing list item[%d], type: %d", currentPlayIndex, type);
            applyListConfig(configList[currentPlayIndex]);
            lastListChangeTime = millis(); // 更新时间戳
            return;
        }

        DEBUG_INFO("⏭️ Skipping empty item[%d]", currentPlayIndex);

    } while (currentPlayIndex != startIndex && attempts < totalListItems);

    // 如果所有项目都是空的
    if (attempts >= totalListItems)
    {
        DEBUG_WARN("⚠️ All list items are empty, stopping playback");
        handleStopListPlayCommand(BluetoothFrame()); // 创建空帧
    }
}

/**
 * 应用列表配置
 */
void applyListConfig(const ListConfig &config)
{
    DisplayType type = getDisplayType(config);
    currentListDisplayType = type; // 缓存显示类型

    DEBUG_INFO("Applying list config, type: %d", type);

    switch (type)
    {
    case GIF_MODE:
        // 仅播放GIF
        DEBUG_INFO("🎬 Applying GIF mode: %s", config.gifFilename);
        if (!playGIFAuto(config.gifFilename))
        {
            DEBUG_ERROR("❌ Failed to play GIF: %s", config.gifFilename);
            playNextListItem(); // 播放失败，跳到下一项
        }
        // 注意：不改变currentloopstate，保持在loop_state_list_new
        break;

    case TEXT_MODE:
        // 应用文本配置
        DEBUG_INFO("📝 Applying text mode");
        // 注意：不改变currentloopstate，保持在loop_state_list_new
        applyTextConfigFromList(config);
        break;

    case OVERLAY_MODE:
        // 先播放GIF背景，再应用文本前景
        DEBUG_INFO("🎭 Applying overlay mode: GIF=%s", config.gifFilename);
        if (playGIFAuto(config.gifFilename))
        {
            // 注意：不改变currentloopstate，保持在loop_state_list_new
            applyTextConfigFromList(config);
        }
        else
        {
            DEBUG_ERROR("❌ Failed to play GIF for overlay: %s", config.gifFilename);
            // 回退到纯文本模式，但仍保持在loop_state_list_new
            applyTextConfigFromList(config);
        }
        break;

    case EMPTY_MODE:
        // 跳过空配置
        DEBUG_WARN("⚠️ Empty config, skipping to next item");
        playNextListItem();
        break;
    }

    // 更新切换时间
    lastListChangeTime = millis();
}

/**
 * 应用文本配置
 */
void applyTextConfigFromList(const ListConfig &config)
{
    if (config.charCount == 0)
    {
        return;
    }

    DEBUG_INFO("📝 Applying text config: font=%d, area=%d, chars=%d, direction=%d",
               config.fontType, config.screenArea, config.charCount, config.displayDirection);

    // 设置字体类型
    currentFontSize = (config.fontType == 0) ? BT_FONT_16x16 : BT_FONT_32x32;

    // 应用显示方向
    handleDirectionCommand(config.displayDirection);
    DEBUG_INFO("🔄 Applied display direction: %s",
               (config.displayDirection == BT_DIRECTION_HORIZONTAL) ? "HORIZONTAL" : "VERTICAL");

    // 应用文本数据
    if (config.fontType == 0)
    {
        // 16x16字体
        switch (config.screenArea)
        {
        case BT_SCREEN_UPPER:
            handleUpperTextCommand((const uint16_t *)config.fontData, config.charCount);
            break;
        case BT_SCREEN_LOWER:
            handleLowerTextCommand((const uint16_t *)config.fontData, config.charCount);
            break;
        case BT_SCREEN_BOTH:
        {
            int halfCount = config.charCount / 2;
            handleUpperTextCommand((const uint16_t *)config.fontData, halfCount);
            handleLowerTextCommand((const uint16_t *)config.fontData + (halfCount * 16),
                                   config.charCount - halfCount);
        }
        break;
        }
    }
    else
    {
        // 32x32字体
        handleFullScreenTextCommand((const uint16_t *)config.fontData, config.charCount);
    }

    // 应用其他配置
    applyColorConfigFromList(config);
    applyEffectConfigFromList(config);
    applyBorderConfigFromList(config);
}

/**
 * 应用颜色配置
 */
void applyColorConfigFromList(const ListConfig &config)
{
    DEBUG_INFO("🎨 Applying color config: RGB(%d,%d,%d), gradient=%d",
               config.textColor[0], config.textColor[1], config.textColor[2], config.gradientMode);

    // 创建临时帧来应用颜色设置
    BluetoothFrame colorFrame;
    colorFrame.isValid = true;
    colorFrame.dataLength = 7;
    colorFrame.data = new uint8_t[7];
    // 根据gradientMode判断颜色模式
    uint8_t colorMode = (config.gradientMode > 0) ? BT_COLOR_MODE_GRADIENT : BT_COLOR_MODE_FIXED;
    DEBUG_INFO("🌈 Color mode determined: %s (gradient=%d)",
               (colorMode == BT_COLOR_MODE_GRADIENT) ? "GRADIENT" : "FIXED", config.gradientMode);

    colorFrame.data[0] = config.screenArea;    // 屏幕区域
    colorFrame.data[1] = BT_COLOR_TARGET_TEXT; // 目标：文本颜色 (0x01)
    colorFrame.data[2] = colorMode;            // 模式：根据gradientMode自动判断
    colorFrame.data[3] = config.textColor[0];  // R
    colorFrame.data[4] = config.textColor[1];  // G
    colorFrame.data[5] = config.textColor[2];  // B
    colorFrame.data[6] = config.gradientMode;  // 渐变模式
    colorFrame.ownsData = true;

    // 调用颜色处理函数
    handleColorCommand(colorFrame);
}

/**
 * 应用特效配置
 */
void applyEffectConfigFromList(const ListConfig &config)
{
    DEBUG_INFO("✨ Applying effect config: type=%d, speed=%d", config.effect, config.effectSpeed);

    // 🔧 修复：先清除之前的特效，避免状态残留
    clearAllEffects(true);  // 清除上半屏特效
    clearAllEffects(false); // 清除下半屏特效
    DEBUG_INFO("🧹 Previous effects cleared");

    // 如果特效类型为0（静态），则只需清除，不设置新特效
    if (config.effect == 0)
    {
        DEBUG_INFO("📌 Static effect - effects cleared");
        return;
    }

    // 创建临时帧来应用特效设置
    BluetoothFrame effectFrame;
    effectFrame.isValid = true;
    effectFrame.dataLength = 3;
    effectFrame.data = new uint8_t[3];
    effectFrame.data[0] = config.screenArea;  // 屏幕区域
    effectFrame.data[1] = config.effect;      // 特效类型
    effectFrame.data[2] = config.effectSpeed; // 特效速度
    effectFrame.ownsData = true;

    // 调用特效处理函数
    handleEffectCommand(effectFrame);
}

/**
 * 应用边框配置
 */
void applyBorderConfigFromList(const ListConfig &config)
{
    DEBUG_INFO("🔲 Applying border config: style=%d, color=%d, effect=%d, speed=%d",
               config.borderStyle, config.borderColorIndex, config.borderEffect, config.borderSpeed);

    // 🔧 修复：先清除之前的边框效果，避免状态残留
    clearBorderEffect();
    DEBUG_INFO("🧹 Previous border effects cleared");

    // 如果边框样式为0（无边框），则只需清除，不设置新边框
    if (config.borderStyle == 0)
    {
        DEBUG_INFO("🚫 No border - border effects cleared");
        return;
    }

    // 创建临时帧来应用边框设置
    BluetoothFrame borderFrame;
    borderFrame.isValid = true;
    borderFrame.dataLength = 4;
    borderFrame.data = new uint8_t[4];
    borderFrame.data[0] = config.borderStyle;      // 边框样式
    borderFrame.data[1] = config.borderColorIndex; // 边框颜色索引
    borderFrame.data[2] = config.borderEffect;     // 边框效果
    borderFrame.data[3] = config.borderSpeed;      // 边框速度
    borderFrame.ownsData = true;

    // 调用边框处理函数
    handleBorderCommand(borderFrame);
}

// ==================== 调试函数实现 ====================

/**
 * 打印列表状态
 */
void printListStatus()
{
    DEBUG_INFO("=== LIST STATUS ===");
    DEBUG_INFO("Total items: %d", totalListItems);
    DEBUG_INFO("Current edit index: %d", currentEditIndex);
    DEBUG_INFO("Current play index: %d", currentPlayIndex);
    DEBUG_INFO("Is edit mode: %s", isEditMode ? "YES" : "NO");
    DEBUG_INFO("Is list playing: %s", isListPlaying ? "YES" : "NO");
    DEBUG_INFO("Last change time: %lu", lastListChangeTime);
    DEBUG_INFO("Current display type: %d", currentListDisplayType);

    for (uint8_t i = 0; i < totalListItems; i++)
    {
        DisplayType type = getDisplayType(configList[i]);
        DEBUG_INFO("List[%d]: Type=%d, GIF='%s', Chars=%d",
                   i, type, configList[i].gifFilename, configList[i].charCount);
    }
    DEBUG_INFO("==================");
}

/**
 * 打印临时配置
 */
void printTempConfig()
{
    DEBUG_INFO("=== TEMP CONFIG ===");
    DEBUG_INFO("GIF filename: '%s'", tempConfigList.gifFilename);
    DEBUG_INFO("Font type: %d (%s)", tempConfigList.fontType,
               (tempConfigList.fontType == 0) ? "16x16" : "32x32");
    DEBUG_INFO("Screen area: %d", tempConfigList.screenArea);
    DEBUG_INFO("Display direction: %d", tempConfigList.displayDirection);
    DEBUG_INFO("Character count: %d", tempConfigList.charCount);
    DEBUG_INFO("Text color: RGB(%d,%d,%d)",
               tempConfigList.textColor[0], tempConfigList.textColor[1], tempConfigList.textColor[2]);
    DEBUG_INFO("Effect: %d, Speed: %d", tempConfigList.effect, tempConfigList.effectSpeed);
    DEBUG_INFO("Border: Style=%d, Color=%d, Effect=%d, Speed=%d",
               tempConfigList.borderStyle, tempConfigList.borderColorIndex,
               tempConfigList.borderEffect, tempConfigList.borderSpeed);
    DEBUG_INFO("Gradient mode: %d", tempConfigList.gradientMode);

    DisplayType type = getDisplayType(tempConfigList);
    DEBUG_INFO("Display type: %d (%s)", type,
               (type == GIF_MODE) ? "GIF" : (type == TEXT_MODE)  ? "TEXT"
                                        : (type == OVERLAY_MODE) ? "OVERLAY"
                                                                 : "EMPTY");
    DEBUG_INFO("==================");
}

/**
 * 打印指定索引的列表配置
 */
void printListConfig(uint8_t index)
{
    if (index >= totalListItems)
    {
        DEBUG_ERROR("❌ Invalid index: %d (total: %d)", index, totalListItems);
        return;
    }

    const ListConfig &config = configList[index];
    DEBUG_INFO("=== LIST[%d] CONFIG ===", index);
    DEBUG_INFO("Sequence: %d", config.sequence);
    DEBUG_INFO("GIF filename: '%s'", config.gifFilename);
    DEBUG_INFO("Font type: %d (%s)", config.fontType,
               (config.fontType == 0) ? "16x16" : "32x32");
    DEBUG_INFO("Screen area: %d", config.screenArea);
    DEBUG_INFO("Display direction: %d", config.displayDirection);
    DEBUG_INFO("Character count: %d", config.charCount);
    DEBUG_INFO("Text color: RGB(%d,%d,%d)",
               config.textColor[0], config.textColor[1], config.textColor[2]);
    DEBUG_INFO("Effect: %d, Speed: %d", config.effect, config.effectSpeed);
    DEBUG_INFO("Border: Style=%d, Color=%d, Effect=%d, Speed=%d",
               config.borderStyle, config.borderColorIndex,
               config.borderEffect, config.borderSpeed);
    DEBUG_INFO("Gradient mode: %d", config.gradientMode);

    DisplayType type = getDisplayType(config);
    DEBUG_INFO("Display type: %d (%s)", type,
               (type == GIF_MODE) ? "GIF" : (type == TEXT_MODE)  ? "TEXT"
                                        : (type == OVERLAY_MODE) ? "OVERLAY"
                                                                 : "EMPTY");
    DEBUG_INFO("======================");
}

/**
 * 🆕 调试函数：列出LittleFS中的所有文件
 */
void printAllFiles()
{
    DEBUG_INFO("=== ALL FILES IN LITTLEFS ===");

    // 列出根目录
    File root = LittleFS.open("/");
    if (!root)
    {
        DEBUG_ERROR("❌ Failed to open root directory");
        return;
    }

    DEBUG_INFO("Root directory files:");
    File file = root.openNextFile();
    while (file)
    {
        if (file.isDirectory())
        {
            DEBUG_INFO("  📁 %s/", file.name());
        }
        else
        {
            DEBUG_INFO("  📄 %s (%d bytes)", file.name(), file.size());
        }
        file = root.openNextFile();
    }

    // 列出gifs目录
    DEBUG_INFO("GIF directory (%s):", GIF_STORAGE_PATH);
    File gifsDir = LittleFS.open(GIF_STORAGE_PATH);
    if (!gifsDir)
    {
        DEBUG_ERROR("❌ Failed to open gifs directory");
        return;
    }

    file = gifsDir.openNextFile();
    bool hasGifs = false;
    while (file)
    {
        if (!file.isDirectory())
        {
            DEBUG_INFO("  🎬 %s (%d bytes)", file.name(), file.size());
            hasGifs = true;
        }
        file = gifsDir.openNextFile();
    }

    if (!hasGifs)
    {
        DEBUG_WARN("⚠️ No GIF files found in %s", GIF_STORAGE_PATH);
    }

    DEBUG_INFO("==============================");
}
