# 列表功能重构方案（最新版）

## 1. 统一配置结构设计（简化版）

```cpp
struct ListConfig {
    uint8_t sequence;        // 序列号（1-32）
    uint8_t reserved[3];     // 预留字段

    // === GIF配置区域 ===
    char gifFilename[32];    // GIF文件名（空字符串表示无GIF）

    // === 文本配置区域 ===
    uint8_t fontType;        // 字体类型：0x00=16x16, 0x01=32x32
    uint8_t screenArea;      // 显示区域：0x01=上半屏, 0x02=下半屏, 0x03=全屏
    uint8_t displayDirection;// 显示方向：0x00=横向显示, 0x01=纵向显示
    uint8_t textColor[3];    // RGB文本颜色
    uint8_t bgColor[3];      // RGB背景颜色（叠层模式下无效）
    uint8_t effect;          // 特效类型：0x00=静态, 0x01=左滚, 0x02=右滚等
    uint8_t effectSpeed;     // 特效速度：1-10
    uint8_t borderStyle;     // 边框样式：0x00=无边框，0x01=实线边框，0x02=点线边框，0x03=角落边框，0x04=彩虹边框
    uint8_t borderColorIndex;// 边框颜色索引：0x00=红色，0x01=绿色，0x02=蓝色，0x03=黄色，0x04=紫色，0x05=青色，0x06=白色
    uint8_t borderEffect;    // 边框效果：0x00=静止显示，0x01=顺时针流动，0x02=逆时针流动，0x03=闪烁效果
    uint8_t borderSpeed;     // 边框速度：1-10（1=最慢，10=最快）
    uint8_t gradientMode;    // 渐变模式：0x00=无, 0x01=垂直, 0x02=水平
    uint8_t charCount;       // 字符数量：0表示无文本，1-100表示字符数
    uint8_t fontData[1600];  // 点阵数据（最大支持50个32x32字符）
};
```

### 显示类型自动判断逻辑

单片机在播放时根据配置内容自动判断显示类型：

```cpp
DisplayType getDisplayType(const ListConfig& config) {
    bool hasGif = (strlen(config.gifFilename) > 0);
    bool hasText = (config.charCount > 0);

    if (hasGif && hasText) return OVERLAY_MODE;      // 叠层模式（GIF背景+文字前景）
    else if (hasGif) return GIF_MODE;               // 纯GIF模式
    else if (hasText) return TEXT_MODE;             // 纯文本模式
    else return EMPTY_MODE;                         // 空配置，跳过
}
```

**判断规则：**

- `gifFilename非空 && charCount=0` → **纯 GIF 模式**
- `gifFilename为空 && charCount>0` → **纯文本模式**
- `gifFilename非空 && charCount>0` → **叠层模式**（GIF 背景+文字前景）
- `gifFilename为空 && charCount=0` → **空配置**（跳过此项）

## 2. 单片机端存储管理

```cpp
// 临时配置区（始终接收最新数据）
ListConfig tempConfigList;

// 正式列表配置数组
ListConfig configList[MAX_LIST_ITEMS];  // 最大支持16个列表项
uint8_t currentEditIndex = 1;           // 当前编辑的列表索引（1-32）
uint8_t totalListItems = 0;             // 当前列表项总数
bool isEditMode = false;                // 是否处于编辑模式
```

## 3. 数据接收和存储流程

### 数据接收规则：

1. **所有配置数据**（文本、颜色、特效、边框、GIF 文件名等）**始终覆盖** `tempConfigList`
2. **只有接收到"保存到列表"命令时**，才将 `tempConfigList` 的内容复制到正式列表
3. **后续发送的任何数据**依旧存入 `tempConfigList`，覆盖之前的临时数据

### 正常添加流程：

1. APP 发送各种配置数据 → **覆盖存储到** `tempConfigList`
2. APP 继续发送更多配置 → **继续覆盖** `tempConfigList`
3. APP 点击"保存到列表"(0x41) → 将 `tempConfigList` **复制到** `configList[currentEditIndex]`
4. `currentEditIndex++` → 自动切换到下一个位置
5. **tempConfigList 数据保持不变** → 用户可基于当前配置继续修改
6. APP 发送下一项的配置 → **覆盖** `tempConfigList`（当需要全新配置时）
7. 重复步骤直到完成所有列表项

### 编辑模式流程：

1. APP 点击"编辑模式" → 发送编辑模式开关命令 (0x42)
2. APP 选择要编辑的列表项 → 发送设置编辑索引命令 (0x40)
3. **将** `configList[editIndex]` **复制到** `tempConfigList` → 用于 APP 显示当前配置
4. APP 修改配置数据 → **覆盖** `tempConfigList`
5. APP 点击"保存修改"(0x41) → 将 `tempConfigList` **复制回** `configList[editIndex]`
6. APP 点击"退出编辑" → 发送编辑模式关闭命令 (0x42)

## 4. 蓝牙协议命令定义

### 4.1 列表管理命令（优化设计）

| 命令码 | 命令名称        | 数据长度 | 说明                                          |
| ------ | --------------- | -------- | --------------------------------------------- |
| 0x40   | 设置编辑索引    | 1 字节   | 设置当前操作的列表索引（1-32）                |
| 0x41   | 保存文本到列表  | 0 字节   | 将 tempConfigList 中的文本配置保存到正式列表  |
| 0x42   | 编辑模式开关    | 1 字节   | 0x00=关闭编辑, 0x01=开启编辑                  |
| 0x43   | 开始列表播放    | 0 字节   | 开始播放当前列表                              |
| 0x44   | 停止列表播放    | 0 字节   | 停止列表播放                                  |
| 0x45   | 清空列表        | 0 字节   | 清空所有列表项                                |
| 0x46   | 删除列表项      | 1 字节   | 删除指定索引的列表项                          |
| 0x47   | 保存 GIF 到列表 | 变长     | 直接将 GIF 文件名保存到正式列表，无需临时配置 |

### 4.2 配置数据命令（复用现有命令，但存储到 tempConfigList）

| 命令码 | 命令名称        | 存储目标       | 说明                      |
| ------ | --------------- | -------------- | ------------------------- |
| 0x04   | 设置文本        | tempConfigList | 覆盖临时配置的文本数据    |
| 0x06   | 设置颜色        | tempConfigList | 覆盖临时配置的颜色数据    |
| 0x08   | 设置特效        | tempConfigList | 覆盖临时配置的特效数据    |
| 0x09   | 设置边框        | tempConfigList | 覆盖临时配置的边框数据    |
| 0x47   | 设置 GIF 文件名 | tempConfigList | 覆盖临时配置的 GIF 文件名 |

## 5. 播放逻辑

### 5.1 列表播放流程

1. 接收到"开始列表播放"(0x43)命令
2. 从 `configList[0]` 开始遍历
3. 对每个配置项调用 `getDisplayType()` 判断显示类型
4. 根据显示类型切换到相应的播放模式：
   - **GIF_MODE** → 切换到 `loop_state_gif`
   - **TEXT_MODE** → 切换到 `loop_state_text`
   - **OVERLAY_MODE** → 切换到 `loop_state_overlay`
   - **EMPTY_MODE** → 跳过，继续下一项
5. 播放完成后自动切换到下一项，循环播放

### 5.2 配置应用逻辑

```cpp
void applyListConfig(const ListConfig& config) {
    DisplayType type = getDisplayType(config);

    switch(type) {
        case GIF_MODE:
            // 仅播放GIF
            playGIF(config.gifFilename);
            currentloopstate = loop_state_gif;
            break;

        case TEXT_MODE:
            // 应用文本配置
            applyTextConfig(config);
            applyColorConfig(config);
            applyEffectConfig(config);
            applyBorderConfig(config);
            currentloopstate = loop_state_text;
            break;

        case OVERLAY_MODE:
            // 先播放GIF背景
            playGIF(config.gifFilename);
            // 再应用文本前景
            applyTextConfig(config);
            applyColorConfig(config);
            applyEffectConfig(config);
            currentloopstate = loop_state_overlay;
            break;

        case EMPTY_MODE:
            // 跳过空配置
            break;
    }
}
```

## 6. 内存管理优化

### 6.1 临时配置区管理

```cpp
// 清空临时配置
void clearTempConfig() {
    memset(&tempConfigList, 0, sizeof(ListConfig));
}

// 复制临时配置到正式列表
void saveTempToList(uint8_t index) {
    if (index < MAX_LIST_ITEMS) {
        memcpy(&configList[index], &tempConfigList, sizeof(ListConfig));
        configList[index].sequence = index + 1;
    }
}

// 复制正式列表到临时配置（编辑模式）
void loadListToTemp(uint8_t index) {
    if (index < MAX_LIST_ITEMS) {
        memcpy(&tempConfigList, &configList[index], sizeof(ListConfig));
    }
}
```

### 6.2 配置验证

```cpp
bool isValidConfig(const ListConfig& config) {
    // GIF文件名验证
    if (strlen(config.gifFilename) > 0) {
        if (!fileExists(config.gifFilename)) return false;
    }

    // 文本配置验证
    if (config.charCount > 0) {
        if (config.charCount > 50) return false;  // 最大50个字符
        if (config.fontType > 1) return false;   // 仅支持0和1
        if (config.screenArea < 1 || config.screenArea > 3) return false;
    }

    return true;
}
```

## 7. 使用场景示例

### 场景 1：创建纯 GIF 列表项

```
1. APP发送: 0x47 "demo.gif"     → 保存GIF到configList[0]，索引自动递增到1
2. 自动判断: hasGif=true, hasText=false → GIF_MODE
3. APP发送: 0x47 "demo2.gif"    → 保存GIF到configList[1]，索引自动递增到2
4. 一步完成，每个0x47创建一个新的GIF项目
```

### 场景 2：创建纯文本列表项

```
1. APP发送: 0x04 [文本数据]     → tempConfigList.fontData = [数据]
2. APP发送: 0x06 [颜色数据]     → tempConfigList.textColor = [颜色]
3. APP发送: 0x08 [特效数据]     → tempConfigList.effect = [特效]
4. APP点击"保存": 0x41          → 将tempConfigList复制到configList[1]
5. 自动判断: hasGif=false, hasText=true → TEXT_MODE
```

### 场景 3：创建叠层模式列表项

```
方案A - 先GIF后文本：
1. APP发送: 0x47 "bg.gif"       → 保存GIF到configList[0]，索引递增到1
2. APP发送: 0x40 0x01           → 设置编辑索引回到0
3. APP发送: 0x04 [文本数据]     → tempConfigList.fontData = [数据]
4. APP发送: 0x06 [颜色数据]     → tempConfigList.textColor = [颜色]
5. APP发送: 0x41               → 将文本配置保存到configList[0]，保留GIF
6. 自动判断: hasGif=true, hasText=true → OVERLAY_MODE

方案B - 先文本后GIF：
1. APP发送: 0x04 [文本数据]     → tempConfigList.fontData = [数据]
2. APP发送: 0x06 [颜色数据]     → tempConfigList.textColor = [颜色]
3. APP发送: 0x41               → 将文本配置保存到configList[0]
4. APP发送: 0x40 0x01           → 设置编辑索引回到0
5. APP发送: 0x47 "bg.gif"       → 保存GIF到configList[0]，索引递增到1
6. 自动判断: hasGif=true, hasText=true → OVERLAY_MODE
```

### 场景 4：编辑已有列表项

```
1. APP点击"编辑模式": 0x42 0x01 → isEditMode = true
2. APP选择编辑项2: 0x40 0x02    → 将configList[2]复制到tempConfigList
3. APP修改文本: 0x04 [新文本]   → tempConfigList.fontData = [新数据]
4. APP点击"保存": 0x41          → 将tempConfigList复制回configList[2]
5. APP退出编辑: 0x42 0x00       → isEditMode = false
```

## 8. 优势总结

1. **简化设计** - 删除了 contentFlags 和 displayMode，由内容自动判断类型
2. **数据安全** - 临时配置区确保数据不会意外丢失
3. **操作直观** - 所有配置先进入临时区，确认后才保存
4. **内存高效** - 只有一个临时配置区，节省内存
5. **扩展性强** - 预留了足够的配置字段
6. **向后兼容** - 复用现有的配置命令，减少开发工作量

---

**注意：此方案完全重新设计了列表功能，不保留原有的 0x30/0x31/0x32 命令，所有数据都通过现有的配置命令+新的列表管理命令来实现。**
