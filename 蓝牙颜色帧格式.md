# 颜色设置蓝牙帧格式说明

## 颜色设置命令 (0x06)

### 命令格式

```
AA 55 06 00 07 [屏幕区域][目标][模式][R][G][B][渐变模式] 0D 0A
```

### 参数说明

- 命令码：0x06
- 数据长度：7 字节
- 屏幕区域：选择设置区域
- 目标：选择文本或背景
- 模式：固定色或渐变色
- R/G/B：RGB 颜色值 (0-255)
- 渐变模式：渐变效果类型(代码中固定七种)

## 参数详细说明

### 屏幕区域 (第 1 字节)

| 值   | 区域   | 说明             |
| ---- | ------ | ---------------- |
| 0x01 | 上半屏 | 仅设置上半屏颜色 |
| 0x02 | 下半屏 | 仅设置下半屏颜色 |
| 0x03 | 全屏   | 同时设置上下半屏 |

### 目标 (第 2 字节)

| 值   | 目标 | 说明         |
| ---- | ---- | ------------ |
| 0x01 | 文本 | 设置文字颜色 |
| 0x02 | 背景 | 设置背景颜色 |

### 模式 (第 3 字节)

| 值   | 模式   | 说明         |
| ---- | ------ | ------------ |
| 0x01 | 固定色 | 单一颜色显示 |
| 0x02 | 渐变色 | 渐变效果显示 |

### 渐变模式 (第 7 字节)

| 值   | 渐变类型         | 说明                   |
| ---- | ---------------- | ---------------------- |
| 0x00 | 固定色           | 无渐变效果             |
| 0x01 | 上下渐变 1       | 彩虹色上下渐变         |
| 0x02 | 上下渐变 2       | 明亮彩虹上下渐变       |
| 0x03 | 上下渐变 3       | 森林色上下渐变         |
| 0x04 | 左右渐变 1       | 火焰色左右渐变         |
| 0x05 | 左右渐变 2       | 霓虹色左右渐变         |
| 0x06 | 左右渐变 3       | 海洋色左右渐变         |
| 0x07 | 左斜 45 度渐变 1 | 日落色左斜 45 度渐变   |
| 0x08 | 左斜 45 度渐变 2 | 海洋色左斜 45 度渐变   |
| 0x09 | 左斜 45 度渐变 3 | 霓虹色左斜 45 度渐变   |
| 0x0A | 右斜 45 度渐变 1 | 极光色右斜 45 度渐变   |
| 0x0B | 右斜 45 度渐变 2 | 彩虹反向右斜 45 度渐变 |
| 0x0C | 右斜 45 度渐变 3 | 暖色调右斜 45 度渐变   |

## 使用示例

### 固定色设置

```
// 全屏白色文字（默认）
AA 55 06 00 07 03 01 01 FF FF FF 00 0D 0A

// 上半屏红色文字
AA 55 06 00 07 01 01 01 FF 00 00 00 0D 0A

// 下半屏蓝色背景
AA 55 06 00 07 02 02 01 00 00 FF 00 0D 0A

// 全屏黑色背景
AA 55 06 00 07 03 02 01 00 00 00 00 0D 0A
```

### 渐变色设置

```
// 上半屏彩虹渐变文字 (上下渐变1)
AA 55 06 00 07 01 01 02 00 00 00 01 0D 0A

// 下半屏火焰渐变文字 (左右渐变1)
AA 55 06 00 07 02 01 02 00 00 00 04 0D 0A

// 全屏霓虹渐变文字 (左右渐变2)
AA 55 06 00 07 03 01 02 00 00 00 05 0D 0A

// 全屏左斜45度日落渐变文字 (左斜45度渐变1)
AA 55 06 00 07 03 01 02 00 00 00 07 0D 0A

// 上半屏左斜45度海洋渐变文字 (左斜45度渐变2)
AA 55 06 00 07 01 01 02 00 00 00 08 0D 0A

// 下半屏左斜45度霓虹渐变文字 (左斜45度渐变3)
AA 55 06 00 07 02 01 02 00 00 00 09 0D 0A

// 全屏右斜45度极光渐变文字 (右斜45度渐变1)
AA 55 06 00 07 03 01 02 00 00 00 0A 0D 0A

// 上半屏右斜45度彩虹反向渐变文字 (右斜45度渐变2)
AA 55 06 00 07 01 01 02 00 00 00 0B 0D 0A

// 下半屏右斜45度暖色调渐变文字 (右斜45度渐变3)
AA 55 06 00 07 02 01 02 00 00 00 0C 0D 0A
```

### 取消渐变色

```
// 取消渐变，恢复最近固定色 (渐变模式设为0x00)
AA 55 06 00 07 03 01 01 FF FF FF 00 0D 0A
```

### 常用颜色 RGB 值

| 颜色 | R   | G   | B   | 十六进制 |
| ---- | --- | --- | --- | -------- |
| 白色 | 255 | 255 | 255 | FF FF FF |
| 红色 | 255 | 0   | 0   | FF 00 00 |
| 绿色 | 0   | 255 | 0   | 00 FF 00 |
| 蓝色 | 0   | 0   | 255 | 00 00 FF |
| 黄色 | 255 | 255 | 0   | FF FF 00 |
| 紫色 | 255 | 0   | 255 | FF 00 FF |
| 青色 | 0   | 255 | 255 | 00 FF FF |
| 黑色 | 0   | 0   | 0   | 00 00 00 |

## 注意事项

1. 渐变色模式下，RGB 值会被忽略，使用预设的渐变组合
2. 设置渐变模式为 0x00 可取消渐变效果
3. 全屏设置会同时影响上下半屏
4. 背景色和文字色可以独立设置
5. 颜色设置立即生效，会触发屏幕重绘

   6.背景不可设置渐变色，只有文本可以设置渐变色
